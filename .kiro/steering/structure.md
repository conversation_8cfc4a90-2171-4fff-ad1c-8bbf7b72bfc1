# Project Structure

## Repository Organization
```
ong-ba-day-hoa/
├── frontend/           # Next.js 15 application
├── backend/            # Strapi 5 CMS
├── database-schema/    # Database documentation
├── docs/              # Project documentation
└── .kiro/             # Kiro AI configuration
```

## Frontend Structure (Next.js 15 App Router)
```
frontend/
├── app/                        # App Router pages and layouts
│   ├── api/                    # API routes and client wrappers
│   │   ├── auth/               # Authentication endpoints
│   │   └── strapi.js           # Main Strapi API client
│   ├── (pages)/                # Route groups for organization
│   │   ├── dang-nhap/          # Login page
│   │   ├── dang-ky/            # Registration page
│   │   ├── khoa-hoc/           # Course pages
│   │   │   └── [slug]/         # Dynamic course detail
│   │   ├── quan-ly/            # Dashboard pages
│   │   │   ├── bai-tap/        # Exercise management
│   │   │   ├── bai-thi/        # Exam management
│   │   │   ├── tai-khoan/      # Account settings
│   │   │   └── xem-video/      # Video viewing
│   │   ├── thanh-toan/         # Payment pages
│   │   └── bai-viet/           # Blog pages
│   ├── layout.js               # Root layout
│   ├── page.js                 # Homepage
│   ├── providers.js            # Context providers
│   └── middleware.js           # Auth middleware
├── components/                 # Reusable React components
│   ├── dashboard/              # Dashboard-specific components
│   ├── icons/                  # Icon components
│   └── layouts/                # Layout components
├── context/                    # React Context providers
│   ├── AuthContext.js          # Authentication state
│   ├── UserProvider.jsx        # User data management
│   └── NotificationContext.jsx # Notification system
├── hooks/                      # Custom React hooks
├── utils/                      # Utility functions
├── data/                       # Static data (schools, etc.)
├── public/                     # Static assets
└── styles/                     # Global CSS
```

## Backend Structure (Strapi 5)
```
backend/
├── src/
│   ├── api/                    # API endpoints and models
│   │   ├── course/             # Course management
│   │   │   ├── content-types/  # Course model definition
│   │   │   ├── controllers/    # Course API controllers
│   │   │   ├── routes/         # Course API routes
│   │   │   └── services/       # Course business logic
│   │   ├── chapter/            # Course chapters
│   │   ├── exercise/           # Practice exercises
│   │   ├── quiz-question/      # Quiz system
│   │   ├── blog-post/          # Blog content
│   │   ├── order/              # Payment orders
│   │   ├── payment/            # Payment processing
│   │   ├── auth/               # Custom auth endpoints
│   │   ├── otp/                # OTP verification
│   │   └── user-activity/      # User progress tracking
│   ├── components/             # Reusable Strapi components
│   │   └── shared/             # Shared component definitions
│   ├── email-templates/        # HTML email templates
│   │   ├── OTP.html            # OTP verification email
│   │   └── Bill.html           # Payment confirmation
│   ├── extensions/             # Plugin extensions
│   │   └── users-permissions/  # Custom user auth logic
│   └── index.ts                # Application entry point
├── config/                     # Strapi configuration
│   ├── database.ts             # MySQL connection config
│   ├── server.ts               # Server configuration
│   ├── admin.ts                # Admin panel config
│   ├── middlewares.ts          # CORS and security
│   └── plugins.ts              # Plugin configuration
├── data/                       # Seed data and uploads
├── public/                     # Public file uploads
├── scripts/                    # Database seeding scripts
└── types/                      # TypeScript type definitions
```

## Key Architectural Patterns

### API Client Pattern
- **Location**: `frontend/app/api/strapi.js`
- **Purpose**: Centralized Axios client with interceptors
- **Features**: Authentication, error handling, request/response transformation

### Authentication Flow
- **Middleware**: `frontend/middleware.js` - Route protection
- **Context**: `frontend/context/AuthContext.js` - Global auth state
- **Backend**: `backend/src/api/auth/` - Custom auth endpoints

### Content Management
- **Content Types**: Defined in `backend/src/api/*/content-types/`
- **Controllers**: Business logic in `backend/src/api/*/controllers/`
- **Services**: Reusable logic in `backend/src/api/*/services/`

### File Organization Conventions

### Frontend Naming
- **Pages**: kebab-case with Vietnamese names (`dang-nhap`, `khoa-hoc`)
- **Components**: PascalCase (`LoginForm`, `CourseCard`)
- **Files**: camelCase for JS/JSX, kebab-case for pages

### Backend Naming
- **API Endpoints**: kebab-case (`course-tier`, `quiz-question`)
- **Database Tables**: snake_case (auto-generated by Strapi)
- **Services**: camelCase methods

### URL Structure
- **Vietnamese URLs**: `/dang-nhap`, `/khoa-hoc/[slug]`, `/quan-ly`
- **API Endpoints**: `/api/courses`, `/api/auth/login`
- **Static Assets**: `/images/`, `/uploads/`

## Database Schema Organization
- **Core Entities**: Users, Courses, Chapters, Exercises
- **Learning**: User Progress, Grades, Streaks
- **Commerce**: Orders, Payments, Vouchers
- **Content**: Blog Posts, FAQs, Testimonials
- **System**: OTP, Activation Codes, Notifications

## Development Workflow
1. **Backend First**: Define content types and API endpoints
2. **Frontend Integration**: Implement UI consuming the APIs
3. **Testing**: Manual testing with real data
4. **Deployment**: Auto-deploy via Dokploy on main branch merge