# Technology Stack

## Architecture
- **Pattern**: Headless CMS architecture with separate frontend and backend
- **Frontend**: Next.js 15 with React 19 (App Router)
- **Backend**: Strapi 5 CMS with TypeScript
- **Database**: MySQL with Knex.js query builder
- **Authentication**: JWT-based with cookie storage

## Frontend Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 19 with functional components and hooks
- **Styling**: Tailwind CSS with custom design system
- **HTTP Client**: Axios with interceptors for API communication
- **State Management**: React Context (AuthContext, UserProvider, NotificationContext)
- **Authentication**: Google OAuth via `@react-oauth/google`
- **Video Player**: HLS.js for video streaming
- **Notifications**: React Hot Toast
- **Icons**: React Icons
- **Routing**: Next.js App Router with middleware for auth

## Backend Stack
- **CMS**: Strapi 5 with TypeScript
- **Runtime**: Node.js (>=18.0.0 <=22.x.x)
- **Database**: MySQL2 driver
- **Email**: Brevo (SendinBlue) provider
- **Authentication**: Strapi Users & Permissions plugin + SSO plugin
- **Payment**: PayOS integration
- **File Storage**: Local file system with public uploads
- **Validation**: Built-in Strapi validation + custom validators

## Development Tools
- **TypeScript**: Backend configuration with CommonJS modules
- **ESLint**: Frontend linting with Next.js config
- **PostCSS**: CSS processing with Tailwind
- **Process Manager**: PM2 for production deployment

## Common Commands

### Backend (Strapi)
```bash
# Development
npm run develop          # Start with auto-reload
npm run build           # Build admin panel
npm run start           # Production start
npm run seed:example    # Seed database with example data

# Deployment
pm2 start ecosystem.config.js
```

### Frontend (Next.js)
```bash
# Development
npm run dev             # Start development server
npm run build           # Build for production
npm run start           # Start production server
npm run lint            # Run ESLint
```

## Environment Requirements
- **Node.js**: >=18.0.0 <=22.x.x
- **MySQL**: Any recent version
- **NPM**: >=6.0.0

## Key Dependencies
- **Frontend**: next, react, tailwindcss, axios, react-cookie, jwt-decode
- **Backend**: @strapi/strapi, mysql2, jsonwebtoken, bcrypt, axios, nodemailer