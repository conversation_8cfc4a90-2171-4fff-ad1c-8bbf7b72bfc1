import { NextResponse } from 'next/server';

// Reusable cache control headers to prevent caching
const NO_CACHE_HEADERS = {
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

/**
 * Enhanced Health check endpoint for deployment monitoring
 * Returns comprehensive application health status with versioning
 */
export async function GET() {
  try {
    // Zero Config: Auto-generate deployment identifier
    // This works by detecting when Dokp<PERSON> restarts the server after deployment
    const processStartTime = Date.now() - (process.uptime() * 1000);
    const buildTime = new Date(processStartTime).toISOString();

    // Try to get commit info from environment (optional for Dokploy)
    const commitSha = process.env.GITHUB_SHA ||           // GitHub Actions (if used)
                     process.env.DEPLOYMENT_ID ||         // Custom deployment ID
                     process.env.npm_package_version ||   // Package version
                     'dokploy'; // Simple identifier for Dokploy deployments

    // Create unique deployment ID based on process start time
    // This changes every time Dokploy restarts the server (i.e., new deployment)
    const deploymentId = `${commitSha.substring(0, 8)}-${processStartTime}`;

    const healthData = {
      status: 'healthy',
      timestamp: Date.now(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),

      // Enhanced deployment tracking
      deploymentId,
      buildTime,
      commitSha: commitSha.substring(0, 8),

      // Cache invalidation helpers
      cacheVersion: deploymentId, // For client-side cache busting
      lastModified: buildTime,

      // Additional metadata for debugging
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };

    return NextResponse.json(healthData, {
      status: 200,
      headers: {
        ...NO_CACHE_HEADERS,
        // Add ETag for conditional requests
        'ETag': `"${deploymentId}"`,
        'Last-Modified': new Date(buildTime).toUTCString(),
        // CORS headers for cross-origin requests
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, If-None-Match, If-Modified-Since'
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);

    return NextResponse.json(
      {
        status: 'error',
        timestamp: Date.now(),
        error: 'Health check failed',
        deploymentId: 'error',
        buildTime: new Date().toISOString()
      },
      {
        status: 500,
        headers: NO_CACHE_HEADERS
      }
    );
  }
}

// Support HEAD requests for basic health checks
export async function HEAD() {
  // Zero Config: Use same logic as GET request
  const processStartTime = Date.now() - (process.uptime() * 1000);
  const buildTime = new Date(processStartTime).toISOString();
  const commitSha = process.env.GITHUB_SHA || 'dokploy';
  const deploymentId = `${commitSha.substring(0, 8)}-${processStartTime}`;

  return new Response(null, {
    status: 200,
    headers: {
      ...NO_CACHE_HEADERS,
      'ETag': `"${deploymentId}"`,
      'Last-Modified': buildTime,
      'X-Deployment-Id': deploymentId,
      'Access-Control-Allow-Origin': '*'
    }
  });
}

// Support OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, If-None-Match, If-Modified-Since',
      'Access-Control-Max-Age': '86400' // 24 hours
    }
  });
}
