import { NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';

// Security headers for cache invalidation endpoint
const SECURITY_HEADERS = {
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block'
};

/**
 * Cache Invalidation API Endpoint
 * Provides programmatic cache invalidation for deployment updates
 * Supports both path-based and tag-based invalidation
 */
export async function POST(request) {
  try {
    // Verify authorization (optional security measure)
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CACHE_INVALIDATION_TOKEN;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401, headers: SECURITY_HEADERS }
      );
    }

    const body = await request.json();
    const { 
      paths = [], 
      tags = [], 
      type = 'both', // 'paths', 'tags', or 'both'
      reason = 'manual'
    } = body;

    const results = {
      success: true,
      invalidated: {
        paths: [],
        tags: []
      },
      errors: [],
      timestamp: Date.now(),
      reason
    };

    // Invalidate specific paths
    if ((type === 'paths' || type === 'both') && paths.length > 0) {
      for (const path of paths) {
        try {
          revalidatePath(path);
          results.invalidated.paths.push(path);
          console.log(`✅ Cache invalidated for path: ${path}`);
        } catch (error) {
          console.error(`❌ Failed to invalidate path ${path}:`, error);
          results.errors.push({
            type: 'path',
            target: path,
            error: error.message
          });
        }
      }
    }

    // Invalidate specific tags
    if ((type === 'tags' || type === 'both') && tags.length > 0) {
      for (const tag of tags) {
        try {
          revalidateTag(tag);
          results.invalidated.tags.push(tag);
          console.log(`✅ Cache invalidated for tag: ${tag}`);
        } catch (error) {
          console.error(`❌ Failed to invalidate tag ${tag}:`, error);
          results.errors.push({
            type: 'tag',
            target: tag,
            error: error.message
          });
        }
      }
    }

    // Default invalidation for common paths if nothing specified
    if (paths.length === 0 && tags.length === 0) {
      const defaultPaths = [
        '/',
        '/quan-ly',
        '/quan-ly/xem-video',
        '/tai-khoan'
      ];

      const defaultTags = [
        'user-data',
        'video-data',
        'course-data'
      ];

      // Invalidate default paths
      for (const path of defaultPaths) {
        try {
          revalidatePath(path);
          results.invalidated.paths.push(path);
        } catch (error) {
          results.errors.push({
            type: 'path',
            target: path,
            error: error.message
          });
        }
      }

      // Invalidate default tags
      for (const tag of defaultTags) {
        try {
          revalidateTag(tag);
          results.invalidated.tags.push(tag);
        } catch (error) {
          results.errors.push({
            type: 'tag',
            target: tag,
            error: error.message
          });
        }
      }
    }

    // Log summary
    console.log('🔄 Cache Invalidation Summary:', {
      paths: results.invalidated.paths.length,
      tags: results.invalidated.tags.length,
      errors: results.errors.length,
      reason: results.reason
    });

    return NextResponse.json(results, {
      status: 200,
      headers: SECURITY_HEADERS
    });

  } catch (error) {
    console.error('Cache invalidation failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Cache invalidation failed',
        message: error.message,
        timestamp: Date.now()
      },
      {
        status: 500,
        headers: SECURITY_HEADERS
      }
    );
  }
}

/**
 * GET endpoint for cache invalidation status/info
 */
export async function GET() {
  try {
    const info = {
      status: 'active',
      supportedOperations: ['path-invalidation', 'tag-invalidation'],
      defaultPaths: [
        '/',
        '/quan-ly',
        '/quan-ly/xem-video',
        '/tai-khoan'
      ],
      defaultTags: [
        'user-data',
        'video-data',
        'course-data'
      ],
      timestamp: Date.now(),
      version: process.env.npm_package_version || '1.0.0'
    };

    return NextResponse.json(info, {
      status: 200,
      headers: SECURITY_HEADERS
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to get cache info' },
      { status: 500, headers: SECURITY_HEADERS }
    );
  }
}

// Support OPTIONS for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      ...SECURITY_HEADERS,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  });
}
