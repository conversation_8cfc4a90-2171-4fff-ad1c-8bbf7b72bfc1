"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '../context/ToastContext';

/**
 * Smart Cache Invalidation Hook
 * Detects deployment updates and automatically refreshes user data
 * Provides seamless user experience during deployments
 */
const useCacheInvalidation = (options = {}) => {
  const {
    checkInterval = 5 * 60 * 1000, // 5 minutes default
    enableAutoRefresh = true,
    enableNotifications = true,
    onVersionChange = null,
    onError = null
  } = options;

  const [deploymentStatus, setDeploymentStatus] = useState('idle');
  const [currentVersion, setCurrentVersion] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState(null);
  
  const router = useRouter();
  const toast = useToast();
  const intervalRef = useRef(null);
  const isInitializedRef = useRef(false);

  // Store version in localStorage for persistence across page reloads
  const getStoredVersion = useCallback(() => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem('app_deployment_version');
    } catch (error) {
      console.warn('Failed to read stored version:', error);
      return null;
    }
  }, []);

  const setStoredVersion = useCallback((version) => {
    if (typeof window === 'undefined') return;
    try {
      if (version) {
        localStorage.setItem('app_deployment_version', version);
      } else {
        localStorage.removeItem('app_deployment_version');
      }
    } catch (error) {
      console.warn('Failed to store version:', error);
    }
  }, []);

  // Check for deployment updates
  const checkForUpdates = useCallback(async () => {
    try {
      setLastCheckTime(Date.now());
      
      const response = await fetch('/api/health', {
        method: 'GET',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }

      const data = await response.json();
      const newVersion = data.deploymentId || data.cacheVersion || data.buildTime;

      if (!newVersion) {
        console.warn('No version identifier found in health check response');
        setDeploymentStatus('healthy');
        return false;
      }

      // Zero Config: Log deployment info for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Zero Config Deployment Detection:', {
          deploymentId: data.deploymentId,
          processUptime: data.uptime,
          buildTime: data.buildTime,
          environment: data.environment
        });
      }

      // Initialize version on first check
      if (!isInitializedRef.current) {
        const storedVersion = getStoredVersion();
        const initialVersion = storedVersion || newVersion;
        setCurrentVersion(initialVersion);
        setStoredVersion(newVersion);
        setDeploymentStatus('healthy');
        isInitializedRef.current = true;
        return false;
      }

      // Check for version change
      if (currentVersion && newVersion !== currentVersion) {
        console.log('🚀 New deployment detected:', {
          old: currentVersion,
          new: newVersion,
          timestamp: new Date().toISOString()
        });

        setCurrentVersion(newVersion);
        setStoredVersion(newVersion);
        setDeploymentStatus('updated');

        // Trigger custom callback if provided
        if (onVersionChange) {
          try {
            await onVersionChange(newVersion, currentVersion);
          } catch (error) {
            console.error('Version change callback failed:', error);
          }
        }

        return true; // Version changed
      }

      setDeploymentStatus('healthy');
      return false; // No version change

    } catch (error) {
      console.error('Cache invalidation check failed:', error);
      setDeploymentStatus('error');
      
      if (onError) {
        onError(error);
      }
      
      return false;
    }
  }, [currentVersion, getStoredVersion, setStoredVersion, onVersionChange, onError]);

  // Auto-refresh user data when deployment is detected
  const refreshUserData = useCallback(async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    
    try {
      // Clear any cached user data
      if (typeof window !== 'undefined') {
        // Clear localStorage cache
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('user') || key.includes('cache') || key.includes('video'))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => {
          try {
            localStorage.removeItem(key);
          } catch (error) {
            console.warn(`Failed to remove ${key}:`, error);
          }
        });
      }

      // Refresh router cache
      router.refresh();

      // Small delay to ensure refresh completes
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (enableNotifications && toast) {
        toast.success(
          'Ứng dụng đã được cập nhật', 
          'Dữ liệu mới nhất đã được tải thành công'
        );
      }

    } catch (error) {
      console.error('Failed to refresh user data:', error);
      
      if (enableNotifications && toast) {
        toast.warning(
          'Có bản cập nhật mới', 
          'Vui lòng làm mới trang (F5) để có trải nghiệm tốt nhất'
        );
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, router, enableNotifications, toast]);

  // Handle deployment updates
  useEffect(() => {
    if (deploymentStatus === 'updated' && enableAutoRefresh) {
      refreshUserData();
    }
  }, [deploymentStatus, enableAutoRefresh, refreshUserData]);

  // Set up periodic checking
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initial check after component mount
    const initialTimeout = setTimeout(() => {
      checkForUpdates();
    }, 2000);

    // Set up interval for periodic checks
    intervalRef.current = setInterval(checkForUpdates, checkInterval);

    // Check when user returns to tab
    const handleVisibilityChange = () => {
      if (!document.hidden && deploymentStatus === 'updated') {
        if (enableNotifications && toast) {
          toast.info(
            'Có bản cập nhật mới', 
            'Ứng dụng sẽ tự động cập nhật dữ liệu'
          );
        }
        if (enableAutoRefresh) {
          refreshUserData();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearTimeout(initialTimeout);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkInterval, checkForUpdates, deploymentStatus, enableAutoRefresh, enableNotifications, refreshUserData, toast]);

  // Manual refresh function
  const manualRefresh = useCallback(async () => {
    const hasUpdate = await checkForUpdates();
    if (hasUpdate || deploymentStatus === 'updated') {
      await refreshUserData();
    } else {
      router.refresh();
      if (enableNotifications && toast) {
        toast.info('Đã làm mới dữ liệu', 'Ứng dụng đã được cập nhật');
      }
    }
  }, [checkForUpdates, deploymentStatus, refreshUserData, router, enableNotifications, toast]);

  return {
    deploymentStatus,
    currentVersion,
    isRefreshing,
    lastCheckTime,
    manualRefresh,
    checkForUpdates
  };
};

export default useCacheInvalidation;
