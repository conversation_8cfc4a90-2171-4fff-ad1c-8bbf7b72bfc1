/**
 * Cache Utilities for Deployment Updates
 * Provides cache busting and invalidation helpers
 */

/**
 * Get current deployment version from localStorage or API
 * @returns {Promise<string|null>} Current deployment version
 */
export const getCurrentVersion = async () => {
  try {
    // Try to get from localStorage first
    const storedVersion = localStorage.getItem('app_deployment_version');
    if (storedVersion) {
      return storedVersion;
    }

    // Fallback to API call
    const response = await fetch('/api/health', {
      method: 'GET',
      cache: 'no-cache'
    });

    if (response.ok) {
      const data = await response.json();
      const version = data.deploymentId || data.cacheVersion || data.buildTime;
      
      if (version) {
        localStorage.setItem('app_deployment_version', version);
        return version;
      }
    }
  } catch (error) {
    console.warn('Failed to get current version:', error);
  }

  return null;
};

/**
 * Add cache busting parameters to URL
 * @param {string} url - Original URL
 * @param {Object} options - Cache busting options
 * @returns {string} URL with cache busting parameters
 */
export const addCacheBusting = (url, options = {}) => {
  const {
    useVersion = true,
    useTimestamp = false,
    customParams = {}
  } = options;

  try {
    const urlObj = new URL(url, window.location.origin);
    
    // Add version parameter
    if (useVersion) {
      const version = localStorage.getItem('app_deployment_version');
      if (version) {
        urlObj.searchParams.set('v', version.substring(0, 8));
      }
    }

    // Add timestamp parameter
    if (useTimestamp) {
      urlObj.searchParams.set('t', Date.now().toString());
    }

    // Add custom parameters
    Object.entries(customParams).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value);
    });

    return urlObj.toString();
  } catch (error) {
    console.warn('Failed to add cache busting to URL:', error);
    return url;
  }
};

/**
 * Create cache-aware fetch wrapper
 * @param {string} url - Request URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} Fetch response
 */
export const cacheBustingFetch = async (url, options = {}) => {
  const {
    cacheBusting = true,
    cacheControl = 'no-cache',
    ...fetchOptions
  } = options;

  let requestUrl = url;
  
  // Add cache busting if enabled
  if (cacheBusting) {
    requestUrl = addCacheBusting(url, {
      useVersion: true,
      useTimestamp: false
    });
  }

  // Add cache control headers
  const headers = {
    'Cache-Control': cacheControl,
    'Pragma': 'no-cache',
    ...fetchOptions.headers
  };

  // Add version header for server-side version checking
  const version = localStorage.getItem('app_deployment_version');
  if (version) {
    headers['X-Client-Version'] = version;
  }

  return fetch(requestUrl, {
    ...fetchOptions,
    headers
  });
};

/**
 * Clear all cache-related data from localStorage
 */
export const clearCacheData = () => {
  try {
    const keysToRemove = [];
    
    // Find all cache-related keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.includes('cache') ||
        key.includes('video') ||
        key.includes('user') ||
        key.includes('deployment')
      )) {
        keysToRemove.push(key);
      }
    }

    // Remove cache keys
    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove cache key ${key}:`, error);
      }
    });

    console.log(`🧹 Cleared ${keysToRemove.length} cache entries`);
    return keysToRemove.length;
  } catch (error) {
    console.error('Failed to clear cache data:', error);
    return 0;
  }
};

/**
 * Invalidate specific cache entries
 * @param {Array<string>} keys - Keys to invalidate
 */
export const invalidateCacheKeys = (keys = []) => {
  let invalidatedCount = 0;
  
  keys.forEach(key => {
    try {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        invalidatedCount++;
      }
    } catch (error) {
      console.warn(`Failed to invalidate cache key ${key}:`, error);
    }
  });

  console.log(`🗑️ Invalidated ${invalidatedCount} cache entries`);
  return invalidatedCount;
};

/**
 * Check if cache is stale based on version
 * @param {string} cacheKey - Cache key to check
 * @param {string} currentVersion - Current deployment version
 * @returns {boolean} True if cache is stale
 */
export const isCacheStale = (cacheKey, currentVersion) => {
  try {
    const cacheData = localStorage.getItem(cacheKey);
    if (!cacheData) return true;

    const parsed = JSON.parse(cacheData);
    const cacheVersion = parsed.version || parsed.deploymentId;
    
    return !cacheVersion || cacheVersion !== currentVersion;
  } catch (error) {
    console.warn('Failed to check cache staleness:', error);
    return true; // Assume stale if we can't check
  }
};

/**
 * Store data with version information
 * @param {string} key - Storage key
 * @param {any} data - Data to store
 * @param {Object} options - Storage options
 */
export const setVersionedCache = (key, data, options = {}) => {
  try {
    const version = localStorage.getItem('app_deployment_version');
    const {
      ttl = null, // Time to live in milliseconds
      metadata = {}
    } = options;

    const cacheEntry = {
      data,
      version,
      timestamp: Date.now(),
      ttl,
      metadata
    };

    localStorage.setItem(key, JSON.stringify(cacheEntry));
    return true;
  } catch (error) {
    console.warn('Failed to set versioned cache:', error);
    return false;
  }
};

/**
 * Get data from versioned cache
 * @param {string} key - Storage key
 * @param {Object} options - Retrieval options
 * @returns {any|null} Cached data or null if stale/missing
 */
export const getVersionedCache = (key, options = {}) => {
  try {
    const {
      checkVersion = true,
      checkTTL = true
    } = options;

    const cacheData = localStorage.getItem(key);
    if (!cacheData) return null;

    const parsed = JSON.parse(cacheData);
    const currentVersion = localStorage.getItem('app_deployment_version');

    // Check version if enabled
    if (checkVersion && currentVersion && parsed.version !== currentVersion) {
      localStorage.removeItem(key); // Clean up stale cache
      return null;
    }

    // Check TTL if enabled
    if (checkTTL && parsed.ttl) {
      const isExpired = Date.now() - parsed.timestamp > parsed.ttl;
      if (isExpired) {
        localStorage.removeItem(key); // Clean up expired cache
        return null;
      }
    }

    return parsed.data;
  } catch (error) {
    console.warn('Failed to get versioned cache:', error);
    return null;
  }
};

/**
 * Trigger cache invalidation via API
 * @param {Object} options - Invalidation options
 * @returns {Promise<boolean>} Success status
 */
export const triggerCacheInvalidation = async (options = {}) => {
  try {
    const {
      paths = [],
      tags = ['user-data', 'video-data'],
      reason = 'manual-trigger'
    } = options;

    const response = await fetch('/api/cache/invalidate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({
        paths,
        tags,
        reason
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Cache invalidation triggered:', result);
      return true;
    } else {
      console.error('❌ Cache invalidation failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to trigger cache invalidation:', error);
    return false;
  }
};
