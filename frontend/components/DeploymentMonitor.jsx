"use client";

import React from 'react';
import useCacheInvalidation from '../hooks/useCacheInvalidation';

/**
 * Enhanced DeploymentMonitor Component
 * Uses smart cache invalidation for seamless deployment updates
 * Automatically refreshes user data without manual intervention
 */
const DeploymentMonitor = ({
  enableInDevelopment = false,
  checkInterval = 5 * 60 * 1000, // 5 minutes
  showDebugInfo = false
}) => {
  // Only run in production unless explicitly enabled for development
  const isProduction = process.env.NODE_ENV === 'production';
  const shouldRun = isProduction || enableInDevelopment;

  // Custom version change handler
  const handleVersionChange = async (newVersion, oldVersion) => {
    console.log('🚀 DeploymentMonitor: Version change detected', {
      from: oldVersion,
      to: newVersion,
      timestamp: new Date().toISOString()
    });

    // Additional custom logic can be added here
    // For example: analytics tracking, user notifications, etc.
  };

  // Custom error handler
  const handleError = (error) => {
    console.warn('DeploymentMonitor: Health check failed', error);

    // Could send error to monitoring service
    // For example: Sentry, LogRocket, etc.
  };

  // Use the smart cache invalidation hook
  const {
    deploymentStatus,
    currentVersion,
    isRefreshing,
    lastCheckTime,
    manualRefresh
  } = useCacheInvalidation({
    checkInterval,
    enableAutoRefresh: true,
    enableNotifications: true,
    onVersionChange: handleVersionChange,
    onError: handleError
  });

  // Don't render anything if not supposed to run
  if (!shouldRun) {
    return null;
  }

  // Debug information (only shown if explicitly enabled)
  if (showDebugInfo && process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-3 rounded-lg text-xs font-mono z-50 max-w-xs">
        <div className="font-bold mb-2">🔧 Deployment Monitor Debug</div>
        <div>Status: <span className="text-green-400">{deploymentStatus}</span></div>
        <div>Version: <span className="text-blue-400">{currentVersion?.substring(0, 12) || 'N/A'}</span></div>
        <div>Refreshing: <span className="text-yellow-400">{isRefreshing ? 'Yes' : 'No'}</span></div>
        <div>Last Check: <span className="text-gray-400">
          {lastCheckTime ? new Date(lastCheckTime).toLocaleTimeString() : 'Never'}
        </span></div>
        <button
          onClick={manualRefresh}
          className="mt-2 bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs"
          disabled={isRefreshing}
        >
          {isRefreshing ? 'Refreshing...' : 'Manual Refresh'}
        </button>
      </div>
    );
  }

  // Production mode: invisible component that works through side effects
  return (
    <div className="hidden">
      {/* This component works through the useCacheInvalidation hook */}
      {/* Status: {deploymentStatus} | Version: {currentVersion} */}
    </div>
  );
};

export default DeploymentMonitor;
