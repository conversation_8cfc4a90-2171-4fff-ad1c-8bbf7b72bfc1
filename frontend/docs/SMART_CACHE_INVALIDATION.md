# 🚀 Smart Cache Invalidation System

## 📋 Tổng Quan

Hệ thống **Smart Cache Invalidation** đư<PERSON><PERSON> thiết kế để giải quyết vấn đề users không thấy được các thay đổi mới sau khi deploy mà không cần phải logout/login hoặc xóa cookies.

## 🎯 Vấn Đề Được Giải Quyết

### Trước Khi Có Solution:
- ❌ Users đã đăng nhập không thấy thay đổi mới sau deployment
- ❌ Phải logout/login lại để thấy updates
- ❌ Video pages hiển thị trang trắng
- ❌ Phải xóa cookies để hoạt động bình thường

### Sau Khi Có Solution:
- ✅ Users tự động thấy thay đổi mới mà không cần làm gì
- ✅ Seamless experience during deployments
- ✅ No white screen issues
- ✅ Automatic data refresh

## 🏗️ Kiến Trúc Hệ Thống

### 1. **Enhanced Health API** (`/api/health`)
```javascript
// Trả về deployment version và metadata
{
  "deploymentId": "abc12345-1640995200000",
  "buildTime": "2025-01-31T10:00:00.000Z",
  "cacheVersion": "abc12345-1640995200000",
  "status": "healthy"
}
```

### 2. **useCacheInvalidation Hook**
- **Monitoring**: Kiểm tra version mỗi 5 phút
- **Detection**: Phát hiện khi có deployment mới
- **Auto-refresh**: Tự động refresh user data
- **Notifications**: Thông báo user về updates

### 3. **Enhanced Components**
- **DeploymentMonitor**: Global monitoring component
- **UserProvider**: Auto-refresh user data
- **Video Components**: Invalidate video cache

### 4. **Cache Utilities**
- **Cache Busting**: Add version parameters to requests
- **Versioned Storage**: Store data with version info
- **Smart Invalidation**: Clear stale cache automatically

## 🔧 Cách Hoạt Động

### Flow Diagram:
```
1. User đang sử dụng app (version A)
2. Deploy version B lên production
3. Health API trả về version B
4. useCacheInvalidation hook detect version change
5. Tự động clear cache và refresh data
6. User thấy version B mà không cần làm gì
```

### Version Detection:
```javascript
// Lưu version trong localStorage
localStorage.setItem('app_deployment_version', 'abc12345-1640995200000');

// So sánh với version mới từ API
if (newVersion !== currentVersion) {
  // Trigger cache invalidation
  refreshUserData();
}
```

## 📁 Files Được Thay Đổi

### Core Files:
1. **`frontend/app/api/health/route.js`** - Enhanced health endpoint
2. **`frontend/hooks/useCacheInvalidation.js`** - Smart cache invalidation hook
3. **`frontend/components/DeploymentMonitor.jsx`** - Global monitoring component
4. **`frontend/utils/cacheUtils.js`** - Cache utilities and helpers

### Integration Files:
5. **`frontend/context/UserProvider.jsx`** - Auto-refresh user data
6. **`frontend/app/quan-ly/xem-video/chi-tiet/page.jsx`** - Video cache invalidation
7. **`frontend/app/api/strapi.js`** - Enhanced API client with cache busting
8. **`frontend/components/AppShell.js`** - DeploymentMonitor integration

### API Files:
9. **`frontend/app/api/cache/invalidate/route.js`** - Cache invalidation API
10. **`frontend/middleware.js`** - Enhanced middleware with version checking

## 🎮 Cách Sử Dụng

### 1. Automatic Mode (Recommended)
```javascript
// Chỉ cần import và sử dụng
import useCacheInvalidation from '@/hooks/useCacheInvalidation';

const { deploymentStatus, currentVersion, manualRefresh } = useCacheInvalidation({
  enableAutoRefresh: true,
  enableNotifications: true
});
```

### 2. Manual Mode
```javascript
// Tự control refresh logic
const { checkForUpdates, manualRefresh } = useCacheInvalidation({
  enableAutoRefresh: false,
  onVersionChange: async (newVersion, oldVersion) => {
    // Custom refresh logic
    await refreshMyData();
  }
});
```

### 3. Component Integration
```javascript
// Trong component cần cache invalidation
import useCacheInvalidation from '@/hooks/useCacheInvalidation';

const MyComponent = () => {
  const refreshComponentData = async () => {
    // Clear component cache
    setData([]);
    // Reload data
    await loadData();
  };

  useCacheInvalidation({
    onVersionChange: refreshComponentData
  });

  return <div>My Component</div>;
};
```

## 🔧 Configuration

### Environment Variables:
```bash
# Optional: Token for cache invalidation API
CACHE_INVALIDATION_TOKEN=your-secret-token

# Build time (auto-generated by deployment)
BUILD_TIME=2025-01-31T10:00:00.000Z

# Deployment ID (auto-generated)
DEPLOYMENT_ID=abc12345
```

### Hook Options:
```javascript
useCacheInvalidation({
  checkInterval: 5 * 60 * 1000,    // Check every 5 minutes
  enableAutoRefresh: true,          // Auto refresh data
  enableNotifications: true,        // Show toast notifications
  onVersionChange: callback,        // Custom version change handler
  onError: errorHandler            // Custom error handler
});
```

## 🧪 Testing

### Local Testing:
```javascript
// Enable debug mode
<DeploymentMonitor 
  enableInDevelopment={true}
  showDebugInfo={true}
/>
```

### Production Testing:
1. Deploy version A
2. Users sử dụng app
3. Deploy version B
4. Observe automatic refresh behavior
5. Check console logs for version changes

## 📊 Monitoring

### Console Logs:
```javascript
🚀 New deployment detected: { old: 'v1', new: 'v2' }
🔄 UserProvider: Refreshing user data after deployment update
🎥 Video Component: Refreshing video data after deployment update
✅ Cache invalidation completed successfully
```

### Success Metrics:
- ✅ Zero user complaints about stale data
- ✅ No logout/login required after deployments
- ✅ No white screen issues
- ✅ Seamless user experience

## 🚨 Troubleshooting

### Common Issues:

1. **Version not detected**
   ```javascript
   // Check health API response
   fetch('/api/health').then(r => r.json()).then(console.log);
   ```

2. **Cache not clearing**
   ```javascript
   // Manual cache clear
   import { clearCacheData } from '@/utils/cacheUtils';
   clearCacheData();
   ```

3. **Hook not working**
   ```javascript
   // Check hook status
   const { deploymentStatus, currentVersion } = useCacheInvalidation();
   console.log({ deploymentStatus, currentVersion });
   ```

## 🔮 Future Enhancements

### Phase 2 Features:
- **Service Worker Integration**: Advanced caching strategies
- **Progressive Web App**: Offline support with cache sync
- **Real-time Updates**: WebSocket-based instant updates
- **Analytics Integration**: Track cache performance metrics

### Advanced Features:
- **Selective Invalidation**: Invalidate specific data types only
- **Background Sync**: Sync data in background when online
- **Conflict Resolution**: Handle concurrent updates gracefully
- **Performance Monitoring**: Track cache hit/miss rates

## 📚 Best Practices

### Do's:
- ✅ Use automatic mode for most components
- ✅ Handle errors gracefully
- ✅ Test thoroughly before production
- ✅ Monitor performance impact

### Don'ts:
- ❌ Don't disable cache invalidation in production
- ❌ Don't set check interval too low (< 1 minute)
- ❌ Don't ignore error callbacks
- ❌ Don't clear all cache unnecessarily

## 🎉 Kết Luận

Smart Cache Invalidation System cung cấp:

1. **Seamless User Experience**: Users không cần làm gì khi có deployment mới
2. **Automatic Updates**: Tự động detect và refresh data
3. **Performance Optimized**: Chỉ refresh khi cần thiết
4. **Developer Friendly**: Easy to integrate và customize
5. **Production Ready**: Tested và optimized cho production environment

Hệ thống này đảm bảo users luôn thấy được version mới nhất mà không cần manual intervention, giải quyết hoàn toàn vấn đề cache stale data sau deployments.
